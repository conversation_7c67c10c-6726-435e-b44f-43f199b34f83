when creating a new use case, you should:

- use DDD principles
- use clean architecture
- create a subpackage in the usecase packages and domain it after the use case.
- create a new packge interactor and a file with the interactor
    - create the a package for inports name `in`  and inside create:
        - a file for the interactor
            - the interactor take the input and return the output and error
        - a file for the inport interface
        - a file for the input struct
        - a file for the output struct
        - a package for the validator and validator file using `https://github.com/go-playground/validator` to validate the inport
    - create a package for the outport name `out` and inside create:
        - a file for the outport interface
        - a file for the output struct
        - a package for the presenter and presenter file
    - create a package for the service and inside create:
        - a file for the service
        - the service should use the inport interface to run the use case and than the result (output + error) of the inport should be passed to the outport interface
        - the service should return the output and error of the outport

--- 

With these steps defined above, create a new use case for the `products` domain, to create a new product and update a product.